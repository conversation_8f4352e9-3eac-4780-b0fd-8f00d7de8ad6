server:
  port: 8080

management:
  endpoints:
    web:
      exposure:
        include: health
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}


spring:
  main:
    allow-circular-references: true
  application:
    name: wormhole-hotelds-api
  nacos:
    config:
      refresh-enabled: true
      enabled: true
  config:
    import:
      - nacos:${spring.application.name}.yml?group=${spring.application.name}&refreshEnabled=true
  cloud:
    nacos:
      server-addr: ${spring.nacos.config.server-addr}
      username: ${spring.nacos.config.username}
      password: ${spring.nacos.config.password}
      encode: UTF-8  # 关键配置
      config:
        server-addr: ${spring.nacos.config.server-addr}
        group: ${spring.application.name}
        username: ${spring.nacos.config.username}
        password: ${spring.nacos.config.password}
        refresh-enabled: true
        enabled: true
        file-extension: yml
      discovery:
        server-addr: ${spring.nacos.config.server-addr}
        group: WORMHOLE_GROUP
        enabled: true
        username: ${spring.nacos.config.username}
        password: ${spring.nacos.config.password}
        register-enabled: false


---
spring:
  config:
    activate:
      on-profile: dev
  nacos:
    config:
      server-addr: 10.95.16.145
---
spring:
  nacos:
    config:
      server-addr: 172.16.0.245
  config:
    activate:
      on-profile: prod

---
spring:
  nacos:
    config:
      server-addr: 10.95.16.145
  config:
    activate:
      on-profile: local
  cloud:
    nacos:
      discovery:
        enabled: false

---
spring:
  config:
    activate:
      on-profile: test
  nacos:
    config:
      server-addr: 10.96.0.4

---
spring:
  config:
    activate:
      on-profile: intl-prod
  nacos:
    config:
      server-addr: nacos-headless.ai-nacos.svc.cluster.local:8848
      username: nacos
      password: b3bjkv65nB

---
spring:
  config:
    activate:
      on-profile: task
  nacos:
    config:
      server-addr: 10.102.0.22