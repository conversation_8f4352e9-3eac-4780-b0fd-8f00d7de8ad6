package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.date.SystemClock;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.model.qo.HdsDeviceQO;
import com.wormhole.hotelds.api.hotel.web.model.req.WebhookTaskBroadcastData;
import com.wormhole.hotelds.api.hotel.web.model.req.YunjiWebhookRequest;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * 云迹开放平台webhook服务
 * 
 * <AUTHOR>
 * @date 2025/8/26
 */
@Service
@Slf4j
public class YunjiWebhookService {
    
    @Autowired
    private HdsDeviceDao hdsDeviceDao;
    
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;
    
    @Autowired
    private RtcService rtcService;
    
    /**
     * 处理云迹机器人任务通知
     * 
     * @param request 云迹回调请求
     * @return 处理结果
     */
    public Mono<Boolean> handleTaskNotification(YunjiWebhookRequest request) {
        log.info("收到云迹任务通知: {}", JacksonUtils.writeValueAsString(request));
        
        // 参数校验
        if (request == null || StringUtils.isAnyBlank(request.getStoreId(), request.getTarget(), request.getStatus())) {
            log.warn("云迹任务通知参数不完整: {}", JacksonUtils.writeValueAsString(request));
            return Mono.just(false);
        }
        
        // 判断任务状态是否是到达目标点位
        if (!"arrived".equals(request.getStatus())) {
            log.info("任务状态不是arrived，跳过处理: status={}", request.getStatus());
            return Mono.just(true);
        }
        
        String hotelCode = request.getStoreId();
        String positionCode = request.getTarget();
        
        return findRoomPadDevice(hotelCode, positionCode)
                .flatMap(device -> {
                    if (device == null) {
                        log.info("未找到对应房间的客房PAD设备: hotelCode={}, positionCode={}", hotelCode, positionCode);
                        return Mono.just(false);
                    }
                    
                    return getRoomInfo(positionCode)
                            .flatMap(position -> sendTaskBroadcastMessage(device, position))
                            .doOnSuccess(result -> log.info("成功发送任务播报消息: deviceId={}, positionCode={}", 
                                    device.getDeviceId(), positionCode))
                            .doOnError(error -> log.error("发送任务播报消息失败: deviceId={}, positionCode={}, error={}", 
                                    device.getDeviceId(), positionCode, error.getMessage(), error));
                })
                .onErrorReturn(false);
    }
    
    /**
     * 查找房间的客房PAD设备
     * 
     * @param hotelCode 酒店编码
     * @param positionCode 位置编码
     * @return 设备信息
     */
    private Mono<HdsDeviceEntity> findRoomPadDevice(String hotelCode, String positionCode) {
        HdsDeviceQO deviceQO = HdsDeviceQO.builder()
                .hotelCode(hotelCode)
                .positionCodes(Collections.singletonList(positionCode))
                .deviceAppType(Collections.singleton(DeviceTypeEnum.ROOM.getCode()))
                .deviceStatus(DeviceStatusEnum.ONLINE.getCode())
                .build();
        
        return hdsDeviceDao.findOne(deviceQO)
                .doOnNext(device -> log.info("找到客房PAD设备: hotelCode={}, positionCode={}, deviceId={}", 
                        hotelCode, positionCode, device.getDeviceId()))
                .doOnError(error -> log.error("查询客房PAD设备失败: hotelCode={}, positionCode={}, error={}", 
                        hotelCode, positionCode, error.getMessage(), error));
    }
    
    /**
     * 获取房间信息
     * 
     * @param positionCode 位置编码
     * @return 房间位置信息
     */
    private Mono<HdsDevicePositionEntity> getRoomInfo(String positionCode) {
        return hdsDevicePositionDao.findByPositionCode(positionCode)
                .doOnNext(position -> log.info("获取房间信息: positionCode={}, positionName={}", 
                        positionCode, position.getPositionName()))
                .doOnError(error -> log.error("获取房间信息失败: positionCode={}, error={}", 
                        positionCode, error.getMessage(), error));
    }
    
    /**
     * 发送任务播报消息
     * 
     * @param device 设备信息
     * @param position 位置信息
     * @return 发送结果
     */
    private Mono<Boolean> sendTaskBroadcastMessage(HdsDeviceEntity device, HdsDevicePositionEntity position) {
        // 生成RTC用户ID
        String rtcUserId = DeviceInitRtcUtil.getRtcUserId(device.getDeviceAppType(), device.getDeviceId());
        
        // 构建播报数据
        WebhookTaskBroadcastData broadcastData = WebhookTaskBroadcastData.builder()
                .category("delivery")
                .msg("您的物品已送达，请前往门口领取。")
                .positionCode(device.getPositionCode())
                .positionName(position != null ? position.getPositionName() : device.getPositionCode())
                .build();
        
        // 构建RTC指令消息
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand("webhook_task_broadcast")
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(broadcastData);
        
        log.info("发送任务播报消息: rtcUserId={}, message={}", rtcUserId, JacksonUtils.writeValueAsString(callbackMessage));
        
        return rtcService.sendDevice(Collections.singletonList(rtcUserId), callbackMessage)
                .then(Mono.just(true));
    }
}
