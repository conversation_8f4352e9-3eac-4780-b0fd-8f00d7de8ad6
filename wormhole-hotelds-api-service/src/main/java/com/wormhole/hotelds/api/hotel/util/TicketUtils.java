package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.common.config.WormholeTranslationConfig;
import com.wormhole.hotelds.api.hotel.constant.ColorEnum;
import cn.hutool.core.util.StrUtil;
import com.wormhole.common.config.WormholeUrlConfig;
import com.wormhole.hotelds.api.hotel.constant.DeviceExceptionTipsEnum;
import com.wormhole.hotelds.api.hotel.storage.config.BucketProperties;
import com.wormhole.hotelds.core.enums.ReplyExceptionEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.ServiceType;
import com.wormhole.hotelds.core.enums.TicketClosedLoopLevel;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import com.wormhole.storage.model.EndpointTypeEnum;
import com.wormhole.storage.model.StorageParams;
import com.wormhole.storage.service.CosObjectStorageService;
import com.wormhole.storage.service.OssObjectStorageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.wormhole.common.config.WormholeTranslationConfig.TranslationKeys.LANGUAGE_EN_US;
import static com.wormhole.common.config.WormholeTranslationConfig.TranslationKeys.LANGUAGE_IN_ID;
import static com.wormhole.hotelds.core.enums.ServiceCategory.*;

@Component
@Slf4j
public class TicketUtils {

    @Autowired
    private WormholeUrlConfig wormholeUrlConfig;

    @Resource
    private BucketProperties bucketProperties;

    @Autowired
    private WormholeTranslationConfig translationConfig;

    @Resource
    @Lazy
    private OssObjectStorageService objectStorageService;

    @Resource
    @Lazy
    private CosObjectStorageService cosObjectStorageService;

    public String getDeviceExceptionTipsTranslate(Integer status,String language){
        String tips = DeviceExceptionTipsEnum.getDeviceExceptionTips(status);
        if (tips == null) return null;
        return translationConfig.translateSync(tips, language);
    }
    public static boolean autoComplete(HdsServiceTicketEntity entity){
        Integer closedLoopLevel = entity.getClosedLoopLevel();
        Integer userType = entity.getUserType();
        TicketClosedLoopLevel level = TicketClosedLoopLevel.getByCode(closedLoopLevel);
        // 创建时完成：ai闭环、sos直通、员工创建
        return  Objects.equals(level, TicketClosedLoopLevel.L1) || Objects.equals(level, TicketClosedLoopLevel.L4) || Objects.equals(userType, 1);
    }

    public static boolean judgeCallNeedFeedback(HdsServiceTicketEntity entity) {
        // 投诉类工单+回复异常工单
        return ObjectUtil.equal(entity.getServiceCategory(), ServiceCategory.COMPLAINT.getCode())
                || ObjectUtil.equal(entity.getReplyExceptionType(), ReplyExceptionEnum.T1.getCode())
                || ObjectUtil.equal(entity.getReplyExceptionType(), ReplyExceptionEnum.T2.getCode());
    }

    public static String getShowTextColor(HdsServiceTicketEntity ticket) {
        if(Objects.equals(ticket.getStatus(),0)) {
            if(Objects.equals(ServiceType.EMERGENCY.name(), ticket.getServiceType()) || Objects.equals(COMPLAINT.getCode(), ticket.getServiceCategory())) {
                return ColorEnum.RED.getHexCode();
            }else {
                return ticket.getOverdueFlag() == 1  ? ColorEnum.ORANGE.getHexCode() :ColorEnum.BLUE.getHexCode();
            }
        } else {
            // 已完成都为灰色
            return ColorEnum.GRAY.getHexCode();
        }
    }

    public static String getCategoryIconKey(String page,String category){
        return String.format("%s_%s",page,category);
    }


    public static String getGuestRequestByLang(HdsServiceTicketEntity entity, String language) {
        String guestRequest = switch (language) {
            case LANGUAGE_EN_US -> entity.getGuestRequestEn();
            case LANGUAGE_IN_ID -> entity.getGuestRequestInd();
            default -> entity.getGuestRequest();
        };
        return StringUtils.isBlank(guestRequest) ? entity.getGuestRequest() : guestRequest;
    }

    public String getCategoryIcon(String page, String serviceCategory) {
        if (StrUtil.equalsAny(serviceCategory,LOCAL_INFO.getCode(),HOTEL_INFO.getCode(),GENERAL.getCode())){
            serviceCategory = ServiceType.INQUIRY.name();
        }
        String categoryIconKey = getCategoryIconKey(page, serviceCategory);
        String objectKey = wormholeUrlConfig.getIconMap().get(categoryIconKey);
        if (StrUtil.isBlank(objectKey)) return null;
        StorageParams build = StorageParams.builder()
                .bucketName(bucketProperties.getCommonBucketName())
                .objectKey(objectKey)
                .build();
        log.info("Get icon key {} url: {}",categoryIconKey, objectKey);
        return cosObjectStorageService.getObjectUrl(build);
    }
}
