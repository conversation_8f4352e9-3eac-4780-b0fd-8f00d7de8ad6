package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.Data;

/**
 * 云迹开放平台机器人任务通知请求参数
 *
 * <AUTHOR>
 * @Date 2025/8/26
 */
@Data
public class YunjiWebhookRequest {

    /**
     * 请求 id
     */
    private String requestId;

    /**
     * 任务唯一标识
     */
    private String taskId;

    /**
     * 机器人当前移动目标点位（对应我们系统的position_code）
     */
    private String target;

    /**
     * 任务状态
     * 可能的值：created, started, arrived, completed, failed, cancelled
     */
    private String status;

    /**
     * 执行任务的设备 ID
     */
    private String deviceId;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 在查询 API 和订单状态变化通知中原样返回，可作为自定义参数使用。长度不超过 1024
     */
    private String attach;

    /**
     * 签名。详见异步返回结果的验签
     */
    private String signature;

    /**
     * 本次任务所属门店（对应我们系统的hotelCode）
     */
    private String storeId;
}
