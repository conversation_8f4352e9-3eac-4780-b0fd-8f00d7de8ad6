package com.wormhole.hotelds.api.hotel.web.model.req;

import lombok.Data;

/**
 * 云迹开放平台机器人任务通知请求参数
 * 
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
public class YunjiWebhookRequest {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 机器人ID
     */
    private String robotId;
    
    /**
     * 门店ID（对应我们系统的hotelCode）
     */
    private String storeId;
    
    /**
     * 任务状态
     * 可能的值：created, started, arrived, completed, failed, cancelled
     */
    private String status;
    
    /**
     * 目标位置（对应我们系统的position_code）
     */
    private String target;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 更新时间
     */
    private Long updateTime;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 扩展信息
     */
    private String extra;
}
