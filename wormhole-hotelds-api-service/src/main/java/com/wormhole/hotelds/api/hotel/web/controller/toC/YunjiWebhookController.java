package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.req.YunjiWebhookRequest;
import com.wormhole.hotelds.api.hotel.web.service.YunjiWebhookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 云迹开放平台消息订阅回调接口
 * 
 * <AUTHOR>
 * @Date 2025/8/26
 */
@RestController
@RequestMapping("/yunji")
@Slf4j
public class YunjiWebhookController {
    
    @Autowired
    private YunjiWebhookService yunjiWebhookService;
    
    /**
     * 云迹机器人任务通知回调接口
     * 接收来自云迹的请求，判断任务状态是否是到达目标点位，
     * 如果是则查找对应房间的客房PAD并进行消息播报提醒
     * 
     * @param request 云迹回调请求参数
     * @return 处理结果
     */
    @PostMapping("/webhook/subscibe")
    public Mono<Result<Boolean>> handleTaskSubscription(@RequestBody YunjiWebhookRequest request) {
        return yunjiWebhookService.handleTaskNotification(request).flatMap(Result::success);
    }
}
