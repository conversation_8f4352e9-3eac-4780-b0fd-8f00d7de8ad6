package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HdsHotelMappingQO implements Serializable {
    private String hotelCode;
    private String channel;
    private String platform;
    private String externalId;
    private List<String> externalIds;
    private boolean includeDeleted = false;


}