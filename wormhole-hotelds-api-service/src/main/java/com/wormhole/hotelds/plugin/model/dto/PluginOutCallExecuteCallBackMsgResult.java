package com.wormhole.hotelds.plugin.model.dto;

import com.wormhole.hotelds.plugin.model.entity.PluginCallRecordDetailEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-29 16:24:56
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginOutCallExecuteCallBackMsgResult {

    private Integer jobId;

    private String phoneNumber;

    private String orderId;

    private List<PluginCallRecordDetailEntity> recordDetailEntities;

}
