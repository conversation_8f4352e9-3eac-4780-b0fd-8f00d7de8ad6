package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.model.req.YunjiWebhookRequest;
import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsDeviceEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 云迹webhook服务测试
 * 
 * <AUTHOR>
 * @date 2025/8/26
 */
@ExtendWith(MockitoExtension.class)
class YunjiWebhookServiceTest {
    
    @Mock
    private HdsDeviceDao hdsDeviceDao;
    
    @Mock
    private HdsDevicePositionDao hdsDevicePositionDao;
    
    @Mock
    private RtcService rtcService;
    
    @InjectMocks
    private YunjiWebhookService yunjiWebhookService;
    
    private YunjiWebhookRequest request;
    private HdsDeviceEntity device;
    private HdsDevicePositionEntity position;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        request = new YunjiWebhookRequest();
        request.setRequestId("req123");
        request.setTaskId("task123");
        request.setTarget("3003");
        request.setStatus("arrived");
        request.setDeviceId("robot001");
        request.setTimestamp(System.currentTimeMillis());
        request.setStoreId("TEST_HOTEL");
        
        device = new HdsDeviceEntity();
        device.setId(1L);
        device.setDeviceId("device123");
        device.setHotelCode("TEST_HOTEL");
        device.setPositionCode("3003");
        device.setDeviceAppType(DeviceTypeEnum.ROOM.getCode());
        device.setDeviceStatus(DeviceStatusEnum.ONLINE.getCode());
        
        position = new HdsDevicePositionEntity();
        position.setId(1L);
        position.setPositionCode("3003");
        position.setPositionName("3003房间");
        position.setHotelCode("TEST_HOTEL");
    }
    
    @Test
    void testHandleTaskNotification_Success() {
        // Mock 依赖调用
        when(hdsDeviceDao.findOne(any())).thenReturn(Mono.just(device));
        when(hdsDevicePositionDao.findByPositionCode("3003")).thenReturn(Mono.just(position));
        when(rtcService.sendDevice(any(), any())).thenReturn(Mono.empty());
        
        // 执行测试
        StepVerifier.create(yunjiWebhookService.handleTaskNotification(request))
                .expectNext(true)
                .verifyComplete();
    }
    
    @Test
    void testHandleTaskNotification_NotArrivedStatus() {
        // 设置非arrived状态
        request.setStatus("started");
        
        // 执行测试
        StepVerifier.create(yunjiWebhookService.handleTaskNotification(request))
                .expectNext(true)
                .verifyComplete();
    }
    
    @Test
    void testHandleTaskNotification_NoDevice() {
        // Mock 没有找到设备
        when(hdsDeviceDao.findOne(any())).thenReturn(Mono.empty());
        
        // 执行测试
        StepVerifier.create(yunjiWebhookService.handleTaskNotification(request))
                .expectNext(false)
                .verifyComplete();
    }
    
    @Test
    void testHandleTaskNotification_InvalidRequest() {
        // 设置无效请求
        request.setStoreId(null);
        
        // 执行测试
        StepVerifier.create(yunjiWebhookService.handleTaskNotification(request))
                .expectNext(false)
                .verifyComplete();
    }
}
