# 云迹开放平台消息订阅回调接口

## 概述

本接口用于接收来自云迹开放平台的机器人任务通知，当机器人到达目标点位时，自动向对应房间的客房PAD推送消息播报提醒。

## 接口信息

- **URL**: `/yunji/webhook/subscibe`
- **方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

```json
{
  "requestId": "string",        // 请求 id
  "taskId": "string",           // 任务唯一标识
  "target": "string",           // 机器人当前移动目标点位（对应系统的position_code）
  "status": "string",           // 任务状态：created, started, arrived, completed, failed, cancelled
  "deviceId": "string",         // 执行任务的设备 ID
  "timestamp": 1693123200000,   // 响应时间戳
  "attach": "string",           // 自定义参数，长度不超过 1024
  "signature": "string",        // 签名
  "storeId": "string"           // 本次任务所属门店（对应系统的hotelCode）
}
```

## 响应格式

```json
{
  "code": "SUCCESS",
  "message": "处理成功",
  "data": true,
  "timestamp": 1693123200000
}
```

## 业务逻辑

1. **参数校验**: 检查必要参数（storeId、target、status）是否完整
2. **状态判断**: 只处理状态为 `arrived` 的任务通知
3. **设备查找**: 根据 `storeId`（hotelCode）和 `target`（position_code）查找对应的客房PAD设备
4. **消息推送**: 如果找到设备，向该设备推送RTC指令消息

## RTC指令消息格式

当机器人到达目标点位时，系统会向客房PAD推送以下格式的消息：

```json
{
  "command": "webhook_task_broadcast",
  "timestamp": "1693123200000",
  "data": {
    "category": "delivery",
    "msg": "您的物品已送达，请前往门口领取。",
    "position_code": "3003",
    "position_name": "3003房间"
  }
}
```

## 使用示例

### 请求示例

```bash
curl -X POST http://localhost:8080/yunji/webhook/subscibe \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "req_123456",
    "taskId": "task_123456",
    "target": "3003",
    "status": "arrived",
    "deviceId": "robot_001",
    "timestamp": 1693123200000,
    "attach": "custom_data",
    "signature": "signature_string",
    "storeId": "HOTEL_001"
  }'
```

### 响应示例

```json
{
  "code": "SUCCESS",
  "message": "处理成功",
  "data": true,
  "timestamp": 1693123200000
}
```

## 错误处理

- 如果参数不完整，返回 `false` 但不抛出异常
- 如果任务状态不是 `arrived`，跳过处理并返回 `true`
- 如果未找到对应的客房PAD设备，记录日志并返回 `false`
- 如果发生异常，返回错误响应

## 日志记录

系统会记录以下关键信息：
- 收到的云迹任务通知
- 设备查找结果
- 消息推送结果
- 异常信息

## 注意事项

1. 只有状态为 `arrived` 的任务才会触发消息推送
2. 只有在线的客房PAD设备才能接收消息
3. 系统会自动生成RTC用户ID进行消息推送
4. 消息内容目前固定为"您的物品已送达，请前往门口领取。"

## 相关文档

- [云迹开放平台API文档](https://docs.yunjiai.cn/v3-beta/subscibe#%E6%9C%BA%E5%99%A8%E4%BA%BA%E4%BB%BB%E5%8A%A1%E9%80%9A%E7%9F%A5)
